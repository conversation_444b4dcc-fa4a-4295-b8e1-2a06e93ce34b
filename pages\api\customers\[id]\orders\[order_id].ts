import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createCancellationApprovalTemplate } from "@/lib/mailer/templates";
import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { BillingAddress, BusinessDetail, Customer, Order, OrderItem, OrderStatus, PublicUser, ShippingAddress } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAuth(
  matchRoute({
    GET: handler,
    PATCH: cancelOrderHandler,
  })
);

export interface PublicUserWithBusinessDetailRelation extends PublicUser {
  business_details?: BusinessDetail[]
}

export interface CustomerWithUserRelation extends Partial<Customer> {
  users?: Partial<PublicUserWithBusinessDetailRelation>;
}

export interface FullOrderDetails extends Order {
  order_items?: OrderItem[];
  shipping_addresses?: ShippingAddress;
  billing_addresses?: BillingAddress;
  order_statuses?: OrderStatus[];
  customers?: Partial<CustomerWithUserRelation>;
}

export interface GetOrderByIdResponse {
  order?: FullOrderDetails;
  error?: string;
}

async function handler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetOrderByIdResponse>
) {
  const { id: user_id, order_id } = req.query;

  const userId = req.user?.id.toString();

  if (!user_id || !order_id) {
    return res.status(400).json({ error: "Missing required fields" });
  }

  if (!userId || userId !== user_id) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .from("customers")
    .select("id")
    .eq("user_id", user_id)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  const order = await supabaseAdminClient
    .from("orders")
    .select(
      "*, order_items(id, order_id, quantity, item_price, products(id, name, sku, image, options, product_group_prices(*)), options), order_statuses(id, status, created_at), shipping_addresses(*), payment_type"
    )
    .eq("id", order_id.toString())
    .eq("user_id", user_id)
    .eq("customer_id", customerId)
    .single();

  if (order.error) {
    return res.status(400).json({ error: order.error.message });
  }

  // Sort order_statuses by created_at
  if (order.data.order_statuses && Array.isArray(order.data.order_statuses)) {
    order.data.order_statuses.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  return res.status(200).json({ order: order.data });
}

export interface CancelOrderResponse {
  order?: Pick<Order, "id">;
  error?: string;
}

async function cancelOrderHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<CancelOrderResponse>
) {
  const { id: user_id, order_id } = req.query;

  const userId = req.user?.id.toString();

  if (!user_id || !order_id) {
    return res.status(400).json({ error: "Missing required fields" });
  }

  if (!userId || userId !== user_id) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .from("customers")
    .select("id")
    .eq("user_id", user_id)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  const order = await supabaseAdminClient
    .from("orders")
    .select(
      `
      id, 
      invoice, 
      order_statuses(status), 
      order_items(
        id, 
        quantity, 
        item_price,
        options,
        products(
          id, 
          name, 
          sku, 
          image, 
          options, 
          price,
          product_group_prices(*)
        )
      ),
      payment_type,
      delivery_method,
      ship_collect,
      ups_account_number,
      purchase_order,
      shipping_address_id,
      billing_address_id,
      total_amount
    `
    )
    .eq("id", order_id.toString())
    .eq("user_id", user_id)
    .eq("customer_id", customerId)
    .single();

  if (order.error) {
    return res.status(400).json({ error: order.error.message });
  }

  const orderStatus = order.data.order_statuses?.[0]?.status;

  if (orderStatus !== "pending") {
    return res.status(400).json({
      error: `This order has a status of "${orderStatus}" and cannot be canceled. Please contact the admin team for assistance with canceling this order.`,
    });
  }

  const orderId = order.data.id;

  const new_order_status = await supabaseAdminClient
    .from("order_statuses")
    .update({ status: "canceled" })
    .eq("order_id", orderId)
    .select("*")
    .single();

  if (new_order_status.error) {
    return res.status(400).json({ error: new_order_status.error.message });
  }

  const now = new Date();
  const updatedAt = now.toISOString();

  await supabaseAdminClient
    .from("orders")
    .update({ updated_at: updatedAt })
    .eq("id", orderId);

  // Get user email for notification
  const userDetails = await supabaseAdminClient
    .from("users")
    .select("email, first_name, last_name")
    .eq("id", user_id)
    .single();

  if (userDetails.data?.email) {
    // Get customer data
    const { data: customerData } = await supabaseAdminClient
      .from("customers")
      .select(
        "company_name, company_website, customer_number, phone, primary_contact_name, role, shipping_notes, user_id"
      )
      .eq("id", customerId)
      .single();

    // Get shipping address for this order
    const { data: shippingAddress } = order.data?.shipping_address_id
      ? await supabaseAdminClient
        .from("shipping_addresses")
        .select(
          "contact_name, address, city, state, zip_code, contact_number, country"
        )
        .eq("id", order.data.shipping_address_id)
        .single()
      : { data: null };

    // Get billing address for this order
    const { data: billingAddress } = order.data?.billing_address_id
      ? await supabaseAdminClient
        .from("billing_addresses")
        .select("address, city, state, zip_code, country")
        .eq("id", order.data.billing_address_id)
        .single()
      : { data: null };

    // Format order items for email template
    const formattedItems =
      order.data.order_items?.map((item) => {
        // Handle options conversion to expected format
        let formattedOptions: {
          name: string;
          value: string;
          price?: number;
        }[] = [];

        if (item.products?.options && Array.isArray(item.products.options)) {
          formattedOptions = item.products.options.map((opt: any) => ({
            name: String(opt?.name || "Option"),
            value: String(opt?.value || ""),
            price: typeof opt?.price === "number" ? opt.price : undefined,
          }));
        }

        return {
          name: item.products?.name || "Product",
          quantity: item.quantity,
          price: item.item_price ?? item.products?.price ?? 0,
          options: formattedOptions,
        };
      }) || [];

    const customerName = `${userDetails.data.first_name || ""} ${userDetails.data.last_name || ""
      }`.trim();

    const invoice =
      order.data.invoice || `#${String(order.data.id).slice(0, 5)}`;
    // Send cancellation email
    const emailTemplate = createCancellationApprovalTemplate({
      to: userDetails.data.email,
      name: customerName,
      firstName: userDetails.data.first_name || undefined,
      lastName: userDetails.data.last_name || undefined,
      companyName: customerData?.company_name || undefined,
      companyWebsite: customerData?.company_website || undefined,
      accountNumber: customerData?.user_id || customerId,
      shippingNotes: customerData?.shipping_notes || undefined,
      orderId: order.data.id,
      invoice: invoice,
      requestDate: new Date().toLocaleString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      }),
      approvalDate: undefined,
      items: formattedItems,
      totalAmount: order.data.total_amount || undefined,
      // Shipping information
      shippingAddress: shippingAddress
        ? {
          contactName: shippingAddress.contact_name || undefined,
          address: shippingAddress.address || undefined,
          city: shippingAddress.city || undefined,
          state: shippingAddress.state || undefined,
          zipCode: shippingAddress.zip_code || undefined,
          phone: shippingAddress.contact_number || undefined,
          country: shippingAddress.country || undefined,
        }
        : undefined,
      // Billing information
      billingAddress: billingAddress
        ? {
          contactName: customerName || undefined,
          address: billingAddress.address || undefined,
          city: billingAddress.city || undefined,
          state: billingAddress.state || undefined,
          zipCode: billingAddress.zip_code || undefined,
          phone: customerData?.phone || undefined,
          country: billingAddress.country || undefined,
        }
        : undefined,
      // Contact and payment info
      phoneNumber: customerData?.phone || undefined,
      paymentMethod: order.data.payment_type || undefined,
      deliveryMethod: order.data.delivery_method || undefined,
      shipCollect: order.data.ship_collect || false,
      upsAccountNumber: order.data.ups_account_number || undefined,
      // Purchase order
      poNumber: order.data.purchase_order || undefined,
      additionalInfo: `Your order has been canceled as requested for Order - ${invoice}. If you have any questions, please contact our customer service team.`,
      orderDetailsUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/store/orders/${order.data.id}`,
    });

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    await sendEmail(mailer, emailTemplate);
  }

  return res.status(200).json({ order: order.data });
}
