import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import createOrderConfirmationTemplate from "@/lib/mailer/templates/order-confirmation";
import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin, checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import {
  Order,
  ProductOption,
  ProductOptionSelect,
  OrderStatus,
} from "@/supabase/types";
import {
  getTaxRateForCity,
  calculateTaxAmount,
} from "../../../lib/utils/nevada-tax-rates";
import { nanoid } from "nanoid";
import { NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: checkAdmin(getOrdersHandler),
  POST: checkAuth(addNewOrderHandler),
});

export const calculatorDataSchema = z
  .object({
    providedData: z
      .object({
        "Jack Type": z.string(),
        "Piston Dia.": z.string(),
        "Car Speed": z.string(),
        "Empty Car": z.string(),
        Capacity: z.string(),
        "Down Speed Regulation": z.string(),
      })
      .optional(),
    results: z
      .object({
        "Rated Flow": z.number(),
        "Empty Static Pressure": z.number(),
        "Loaded Car Pressure": z.number(),
      })
      .optional(),
  })
  .optional();

export type CalculatorData = z.infer<typeof calculatorDataSchema>;
export type AddNewOrder = z.infer<typeof addNewOrderSchema>;

const addNewOrderSchema = z.object({
  billingAddressId: z.string().min(1, "Please select a billing address"),
  shippingAddressId: z.string().min(1, "Please select a shipping address"),
  ship_collect: z.boolean().default(false),
  ups_account_number: z.string().optional(),
  delivery_method: z.string().min(1, "Please select a delivery method"),
  poNumber: z.string().optional(),
  poFile: z.string().optional(),
  tax_exempt: z.boolean().default(false),
  paymentType: z.enum(["credit_card", "purchase_order"]),
  items: z
    .array(
      z.object({
        id: z.string(),
        quantity: z.number().min(1),
        options: z
          .array(
            z.object({
              name: z.string(),
              value: z.union([z.string(), z.number()]),
            })
          )
          .optional(),
        calculatorData: calculatorDataSchema,
      })
    )
    .default([]),
  notes: z.string().max(500, "Notes must be 500 characters or less").optional(),
});

export interface AddNewOrderResponse {
  error?: string;
  order_id?: string;
}

const MINIMUM_ORDER_AMOUNT = 50;

async function addNewOrderHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse
) {
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const data = addNewOrderSchema.safeParse(req.body);

  if (data.error) {
    return res.status(400).json({ error: data.error.issues[0].message });
  }

  const {
    billingAddressId,
    shippingAddressId,
    ship_collect,
    ups_account_number,
    delivery_method,
    poNumber,
    poFile,
    items,
    paymentType,
    tax_exempt,
    notes,
  } = data.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  // Get the customer id
  const customer = await supabaseAdminClient
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  // Get the customer's group separately
  const customerGroup = await supabaseAdminClient
    .from("customer_groups")
    .select("group_id")
    .eq("customer_id", customerId)
    .single();

  const customerGroupId = customerGroup.data?.group_id;
  const invoice = nanoid(5).toUpperCase();

  // Fetch product prices
  const productIds = items.map((item) => item.id);
  const products = await supabaseAdminClient
    .from("products")
    .select(
      "id, name, price, options, product_group_prices(id, group_id, custom_price)"
    )
    .in("id", productIds);

  if (products.error) {
    return res.status(400).json({ error: products.error.message });
  }

  const cartSubtotal = items.reduce((total, item) => {
    const product = products.data?.find((p) => p.id === item.id);

    // Check for group pricing first
    let productPrice = 0;
    if (customerGroupId && product?.product_group_prices) {
      // Ensure product_group_prices is treated as an array
      const groupPrices = Array.isArray(product.product_group_prices)
        ? product.product_group_prices
        : [];

      // Find a group price that matches the customer's group
      const groupPrice = groupPrices.find((gp) => {
        // Using a type assertion to access group_id safely
        const gpObj = gp as any;
        return gpObj.group_id === customerGroupId;
      });

      // Use custom_price if available, otherwise fall back to regular price
      if (groupPrice && typeof (groupPrice as any).custom_price === "number") {
        productPrice = (groupPrice as any).custom_price;
      } else {
        productPrice = product?.price || 0;
      }
    } else {
      productPrice = product?.price || 0;
    }

    // Calculate option prices if options exist
    let optionsPrice = 0;
    if (item.options && product?.options) {
      optionsPrice = item.options.reduce((optTotal, selectedOption) => {
        // Find the matching option in product options
        const productOptions = product.options as unknown as ProductOption[];
        const productOption = productOptions.find(
          (opt) => opt.name === selectedOption.name
        );

        // Find the matching option value to get price
        let optionPrice = 0;
        if (productOption && productOption.type === "select") {
          const selectOption = productOption as ProductOptionSelect;
          const selectedValue = selectOption.options.find(
            (opt) =>
              opt.value === selectedOption.value ||
              opt.name === selectedOption.value
          );
          optionPrice = selectedValue?.price || 0;
        }

        return optTotal + optionPrice;
      }, 0);
    }

    // Calculate total for this item: (base price + options price) * quantity
    const itemTotal = (productPrice + optionsPrice) * item.quantity;
    return total + itemTotal;
  }, 0);

  if (cartSubtotal < MINIMUM_ORDER_AMOUNT) {
    return res.status(400).json({
      error: `Order total must be at least $${MINIMUM_ORDER_AMOUNT}.`,
    });
  }

  // Get shipping address to calculate tax
  const shippingAddressResult = await supabaseAdminClient
    .from("shipping_addresses")
    .select(
      "contact_name, address, city, state, zip_code, contact_number, country"
    )
    .eq("id", shippingAddressId)
    .single();

  if (shippingAddressResult.error) {
    return res.status(400).json({ error: "Invalid shipping address" });
  }

  const shippingAddressData = shippingAddressResult.data;

  // Calculate tax rate and amount (server-side only, cannot be tampered with)
  // Get all tax rates from database and pass to the utility function
  let calculatedTaxRate = 0;

  if (
    shippingAddressData.state === "Nevada" ||
    shippingAddressData.state === "NV"
  ) {
    // Get all tax rates from database
    const { data: taxRatesFromDB } = await supabaseAdminClient
      .schema("public")
      .from("nevada_tax_rates")
      .select("*");

    // Use the utility function with tax rates data
    calculatedTaxRate = getTaxRateForCity(
      shippingAddressData.city || "",
      shippingAddressData.state || "",
      taxRatesFromDB || undefined
    );
  }

  const calculatedTaxAmount = calculateTaxAmount(
    cartSubtotal,
    calculatedTaxRate,
    tax_exempt
  );

  const totalWithTax = cartSubtotal + calculatedTaxAmount;

  const order = await supabaseAdminClient
    .from("orders")
    .insert({
      customer_id: customerId,
      user_id: userId,
      purchase_order: poNumber,
      po_attachment: poFile,
      billing_address_id: billingAddressId,
      shipping_address_id: shippingAddressId,
      invoice,
      payment_type: paymentType,
      delivery_method: delivery_method,
      ship_collect: ship_collect,
      ups_account_number: ups_account_number,
      total_amount: totalWithTax,
      tax_exempt,
      tax_rate: calculatedTaxRate,
      tax_amount: calculatedTaxAmount,
      notes,
    })
    .select("id, created_at")
    .single();

  if (order.error) {
    return res.status(400).json({ error: order.error.message });
  }

  const orderStatus = await supabaseAdminClient
    .from("order_statuses")
    .insert({
      status: "pending",
      order_id: order.data.id,
      customer_id: customerId,
    })
    .select("id")
    .single();

  if (orderStatus.error) {
    // Rollback order creation

    await supabaseAdminClient
      .from("orders")
      .delete()
      .eq("id", order.data.id)
      .eq("user_id", userId)
      .eq("customer_id", customerId);

    return res.status(400).json({ error: orderStatus.error.message });
  }

  const orderId = order.data.id;

  const orderItems = items.map((item) => {
    const product = products.data?.find((p) => p.id === item.id);

    // Determine product price (check for group pricing)
    let baseProductPrice = product?.price || 0;
    if (customerGroupId && product?.product_group_prices) {
      const groupPrices = Array.isArray(product.product_group_prices)
        ? product.product_group_prices
        : [];

      const groupPrice = groupPrices.find((gp) => {
        const gpObj = gp as any;
        return gpObj.group_id === customerGroupId;
      });

      // Use custom_price if available
      if (groupPrice && typeof (groupPrice as any).custom_price === "number") {
        baseProductPrice = (groupPrice as any).custom_price;
      }
    }

    // Calculate option prices if options exist
    let optionsWithPrices = item.options;
    let totalOptionsPrice = 0;
    if (item.options && product?.options) {
      optionsWithPrices = item.options.map((selectedOption) => {
        // Find the matching option in product options
        const productOptions = product.options as unknown as ProductOption[];
        const productOption = productOptions.find(
          (opt) => opt.name === selectedOption.name
        );

        // Find the matching option value to get price
        let optionPrice = 0;
        if (productOption && productOption.type === "select") {
          const selectOption = productOption as ProductOptionSelect;
          const selectedValue = selectOption.options.find(
            (opt) =>
              opt.value === selectedOption.value ||
              opt.name === selectedOption.value
          );
          optionPrice = selectedValue?.price || 0;
          totalOptionsPrice += optionPrice;
        }

        // Return option with price
        return {
          ...selectedOption,
          price: optionPrice,
        };
      });
    }

    // Calculate total price for this item
    const itemPrice = baseProductPrice;

    return {
      order_id: orderId,
      customer_id: customerId,
      product_id: item.id,
      quantity: item.quantity,
      options: optionsWithPrices ?? undefined,
      item_price: itemPrice,
      calculator_data: item.calculatorData,
    };
  });

  const newOrder = await supabaseAdminClient
    .from("order_items")
    .insert(orderItems)
    .select("id");

  if (newOrder.error) {
    // Rollback order creation

    await supabaseAdminClient
      .from("orders")
      .delete()
      .eq("id", order.data.id)
      .eq("user_id", userId)
      .eq("customer_id", customerId);

    return res.status(400).json({ error: newOrder.error.message });
  }

  const userDetails = await supabaseAdminClient
    .from("users")
    .select("email, first_name, last_name, phone")
    .eq("id", userId)
    .single();

  // Fetch customer details for the email
  const customerDetails = await supabaseAdminClient
    .from("customers")
    .select(
      "company_name, company_website, customer_number, shipping_notes, users(business_details(maxton_account)), user_id"
    )
    .eq("id", customerId)
    .single();

  // Use the shipping address data we already fetched
  const shippingAddress = { data: shippingAddressData, error: null };

  // Get billing address for this order
  const billingAddress = billingAddressId
    ? await supabaseAdminClient
        .from("billing_addresses")
        .select("address, city, state, zip_code, country")
        .eq("id", billingAddressId)
        .single()
    : { data: null, error: null };

  if (userDetails.data?.email) {
    const orderItemsWithDetails = orderItems.map((item) => {
      const product = products.data?.find((p) => p.id === item.product_id);

      // Format options to match the email template requirements
      const formattedOptions = item.options?.map((option) => {
        // Type assertion to access price
        const optionWithPrice = option as {
          name: string;
          value: string | number;
          price?: number;
        };

        return {
          name: optionWithPrice.name,
          value: optionWithPrice.value?.toString() || "", // Convert any number values to strings
          price: optionWithPrice.price, // Ensure price is included
        };
      });

      return {
        name: product?.name ?? "Product",
        quantity: item.quantity,
        price: product?.price, // Include original price for reference
        item_price: item.item_price, // Use item_price which includes group discounts and options
        options: formattedOptions,
        calculator_data: item.calculator_data,
      };
    });

    try {
      const mailerOptions = createMailerOptions();
      const mailer = createMailer(mailerOptions);

      // Create a display name from first and last name
      const displayName = `${userDetails.data.first_name || ""} ${
        userDetails.data.last_name || ""
      }`.trim();

      const emailTemplate = createOrderConfirmationTemplate({
        to: userDetails.data.email,
        name: displayName,
        firstName: userDetails.data.first_name || undefined,
        lastName: userDetails.data.last_name || undefined,
        companyName: customerDetails.data?.company_name || undefined,
        companyWebsite: customerDetails.data?.company_website || undefined,
        accountNumber: customerDetails.data?.user_id || customerId,
        phoneNumber: userDetails.data?.phone || undefined,
        shippingNotes: customerDetails.data?.shipping_notes || undefined,
        orderId: order.data.id,
        invoice: invoice,
        items: orderItemsWithDetails,
        poNumber: poNumber,
        maxtonAccountNum: customerDetails?.data?.users?.business_details?.[0]
          ?.maxton_account
          ? parseInt(
              customerDetails?.data?.users?.business_details?.[0]
                ?.maxton_account
            )
          : undefined,
        totalAmount: totalWithTax, // Include the total amount with tax in the email
        subtotalAmount: cartSubtotal,
        taxAmount: calculatedTaxAmount,
        taxRate: calculatedTaxRate,
        taxExempt: tax_exempt,
        // Shipping information
        shippingAddress: shippingAddress.data
          ? {
              contactName: shippingAddress.data.contact_name || undefined,
              address: shippingAddress.data.address || undefined,
              city: shippingAddress.data.city || undefined,
              state: shippingAddress.data.state || undefined,
              zipCode: shippingAddress.data.zip_code || undefined,
              phone: shippingAddress.data.contact_number || undefined,
              country: shippingAddress.data.country || undefined,
            }
          : undefined,
        // Billing information
        billingAddress: billingAddress.data
          ? {
              contactName: displayName || undefined,
              address: billingAddress.data.address || undefined,
              city: billingAddress.data.city || undefined,
              state: billingAddress.data.state || undefined,
              zipCode: billingAddress.data.zip_code || undefined,
              phone: userDetails.data?.phone || undefined,
              country: billingAddress.data.country || undefined,
            }
          : undefined,
        // Payment and delivery information
        paymentMethod: paymentType,
        deliveryMethod: delivery_method,
        shipCollect: ship_collect,
        upsAccountNumber: ups_account_number,
        // Use the order's created_at timestamp from the database
        datePurchased: order.data.created_at || new Date().toISOString(),
        notes: notes,
      });

      await sendEmail(mailer, emailTemplate);
    } catch (error) {
      console.error("Failed to send order confirmation email:", error);
    }
  }

  return res.status(200).json({ order_id: order.data.id });
}

export interface GetOrdersResponse {
  data?: Order[];
  error?: string;
  total?: number;
  totalPages?: number;
}

async function getOrdersHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetOrdersResponse>
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;
  const search = query.search ? String(query.search).toLowerCase().trim() : "";
  const sortBy = query.sortBy ? String(query.sortBy) : "created_at";
  const sortOrder = query.sortOrder ? String(query.sortOrder) : "desc";
  const statusFilter = query.status ? String(query.status) : undefined;

  const supabaseAdminClient = createSupabaseAdminClient();
  const ascending = sortOrder === "asc";

  // Columns that require complex sorting (fetch all, sort client-side, then paginate)
  const columnSort = {
    totalAmount: "total_amount",
    totalItems: "quantity",
    customer_data_company_name: "company_name",
    maxton_account: "maxton_account",
    customer_data_user_data_email: "email",
    id: "id",
    tax_exempt: "tax_exempt",
    tracking_link: "tracking_link",
    currentStatus: "status",
    created_at: "created_at",
  };

  try {
    let orderIds: string[] = [];
    let count = 0;

    const order_by_col = columnSort[sortBy] || "created_at";

    const { count: totalCount } = await supabaseAdminClient
      .from("orders")
      .select("id", { count: "exact" });

    const allOrderIds = await supabaseAdminClient.rpc(
      "get_order_ids_with_count",
      {
        limit_rows: limit,
        page_num: page,
        order_by_col,
        sort_order: sortOrder,
        search,
        status_filter: statusFilter,
      }
    );
    const allOrderIdsData = allOrderIds.data?.at(0);
    count = allOrderIdsData?.total_count || 0;
    orderIds = allOrderIdsData?.ids || [];

    const data = await supabaseAdminClient
      .from("orders")
      .select(
        `
        *,
        order_items(id, order_id, quantity, products(*), options, item_price),
        order_statuses(id, status, created_at),
        customer_data:customers(
          *,
          user_data:users(
            *,
            business_details(
              id,
              maxton_account
            )
          )
        )
      `
      )
      .in("id", orderIds || []);

    if (data.error) {
      return res.status(400).json({ error: data.error.message });
    }

    let paginatedData = data.data;

    if (order_by_col === "quantity") {
      paginatedData = paginatedData.sort((a, b) => {
        const totalItemsA = a.order_items.reduce(
          (acc, item) => acc + item.quantity,
          0
        );
        const totalItemsB = b.order_items.reduce(
          (acc, item) => acc + item.quantity,
          0
        );
        return ascending
          ? totalItemsA - totalItemsB
          : totalItemsB - totalItemsA;
      });
    }

    if (order_by_col === "total_amount") {
      paginatedData = paginatedData.sort((a, b) => {
        return ascending
          ? (a.total_amount || 0) - (b.total_amount || 0)
          : (b.total_amount || 0) - (a.total_amount || 0);
      });
    }

    if (order_by_col === "created_at" && sortOrder === "asc") {
      paginatedData = paginatedData.sort((a, b) => {
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return dateA - dateB;
      });
    } else if (order_by_col === "created_at" && sortOrder === "desc") {
      paginatedData = paginatedData.sort((a, b) => {
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return dateB - dateA;
      });
    }

    const total = count || totalCount || 0;
    const totalPages = Math.max(1, Math.ceil(total / limit));

    return res.status(200).json({
      data: paginatedData || [],
      total,
      totalPages,
    });
  } catch (error: any) {
    console.error("Error in getOrdersHandler:", error);
    return res.status(400).json({
      error: error.message || "An error occurred while fetching orders data",
    });
  }
}
