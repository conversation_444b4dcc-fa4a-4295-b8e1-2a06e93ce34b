import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Grid } from "@stackshift-ui/grid";
import { GridItem } from "@stackshift-ui/grid-item";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { SocialIcons } from "@stackshift-ui/social-icons";
import { Text } from "@stackshift-ui/text";

import { FooterProps } from ".";
import { logoLink } from "helper";
import { ContactDetails, Images, Logo, SocialLink } from "types";
import { Socials } from "@stackshift-ui/footer/dist/types";
import { getImageDimensions } from "@sanity/asset-utils";
import React from "react";

export default function Footer_E({
  logo,
  text,
  contacts,
  copyright,
  socialMedia,
  images,
}: FooterProps) {
  return (
    <React.Fragment>
      <Section className="py-5 md:py-10 bg-primary">
        <Container maxWidth={1240} className="!px-0">
          <Flex className="gap-8 md:gap-20 flex-col md:flex-row ">
            <div className="w-full md:w-[40%]">
              <div className="">
                <LogoSection logo={logo} />
                <TextContainer text={text} />
              </div>
            </div>
            <div className="w-[60%]">
              <ContactsContainer contacts={contacts} />
              {images && (
                <Flex
                  direction="row"
                  align="center"
                  justify="start"
                  wrap
                  className="mb-2 gap-6 md:gap-12"
                >
                  <ImagesSection images={images} />
                </Flex>
              )}
            </div>
          </Flex>
        </Container>
      </Section>
      <Flex
        md="row"
        justify="between"
        align="center"
        className="gap-4 md:gap-0 flex-col md:flex-row bg-primary "
      >
        <Container maxWidth={1240} className="!px-0 py-5 border-t border-white">
          <CopyrightContainer copyright={copyright} />
          <SocialMediaContainer socialMedia={socialMedia} />
        </Container>
      </Flex>
    </React.Fragment>
  );
}

function LogoSection({ logo }: { logo?: Logo }) {
  if (!logo?.image) return null;

  const width = getImageDimensions(logo?.image)?.width;
  const height = getImageDimensions(logo?.image)?.height;
  return (
    <div className="mb-6">
      <Link
        aria-label={
          logoLink(logo) === "/" ? "Go to home page" : `Go to ${logoLink(logo)}`
        }
        className="inline-block"
        href={logoLink(logo)}
        target={logo?.linkTarget}
        rel={logo?.linkTarget === "_blank" ? "noopener noreferrer" : ""}
      >
        <Image
          src={`${logo?.image}`}
          width={width}
          height={height}
          alt={logo?.alt ?? "footer-logo"}
          className="h-auto"
        />
      </Link>
    </div>
  );
}

function TextContainer({ text }: { text?: string }) {
  if (!text) return null;

  return <Text className="!text-sm leading-relaxed text-white">{text}</Text>;
}

function ContactsContainer({ contacts }: { contacts?: ContactDetails[] }) {
  if (!contacts) return null;

  return (
    <div className="w-full mb-5">
      {contacts?.map((contact, index) => (
        <div className="flex flex-col gap-6" key={index}>
          <Text weight="bold" className="mb-2 text-xl md:text-2xl text-white">
            Get in Touch with Us
          </Text>
          <div className="grid grid-cols-2 gap-y-5">
            {contact?.addressInfo && (
              <div>
                <Text weight="semibold" className=" text-base text-white">
                  Address
                </Text>
                <Text className="h-[21px] !text-sm text-gray-300">
                  {contact?.addressInfo}
                </Text>
              </div>
            )}

            {contact?.emailInfo && (
              <div>
                <Text weight="semibold" className=" text-base text-white">
                  Email
                </Text>
                <Link
                  href={`mailto:${contact?.emailInfo}`}
                  className="h-[21px] block !text-sm text-gray-300 hover:text-white transition-colors -mt-[1px]"
                >
                  {contact?.emailInfo}
                </Link>
              </div>
            )}

            {contact?.contactInfo && (
              <div className="">
                <Text weight="semibold" className=" text-base text-white">
                  Number
                </Text>
                <Link
                  href={`tel:${contact?.contactInfo}`}
                  className="h-[21px] !text-sm text-gray-300 hover:text-white transition-colors"
                >
                  {contact?.contactInfo}
                </Link>
              </div>
            )}

            {contact?.faxInfo && (
              <div>
                <Text weight="semibold" className=" text-base text-white">
                  Fax
                </Text>
                <Text className="h-[21px] !text-sm text-gray-300">
                  {contact?.faxInfo}
                </Text>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

function ImagesSection({ images }: { images?: Images[] }) {
  if (!images || images.length === 0) return null;

  return images?.map((image, index) => (
    <div key={index} className="flex items-center">
      <Image
        src={`${image?.image}`}
        width={getImageDimensions(image?.image)?.width}
        height={getImageDimensions(image?.image)?.height}
        alt={image?.alt ?? "certification-logo"}
        className="brightness-0 invert-[1] h-auto w-20 md:w-32"
      />
    </div>
  ));
}

function CopyrightContainer({ copyright }: { copyright?: string }) {
  if (!copyright) return null;

  const currentYear = new Date().getFullYear();
  return (
    <Flex justify="between" align="center" className="w-full">
      <Text className="text-xs md:text-sm text-gray-300">
        © Copyright {currentYear} {copyright}
      </Text>
      <Text className="text-xs md:text-sm text-gray-300">
        Designed and Powered by&nbsp;
        <Link
          href="https://www.webriq.com/"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors"
        >
          WebriQ
        </Link>
      </Text>
    </Flex>
  );
}

function SocialMediaContainer({ socialMedia }: { socialMedia?: SocialLink[] }) {
  if (!socialMedia) return null;

  return (
    <Flex wrap className="gap-2 md:gap-4">
      {socialMedia?.map((social, index) => (
        <SocialMediaLink key={index} social={social} />
      ))}
    </Flex>
  );
}

function SocialMediaLink({ social }: { social?: SocialLink }) {
  if (!social?.socialMediaLink) return null;

  return (
    <Link
      aria-label={social?.socialMedia || social?.socialMediaPlatform || ""}
      className="p-2 rounded hover:bg-white/10 transition-colors"
      target="_blank"
      rel="noopener noreferrer"
      href={social?.socialMediaLink}
    >
      {social?.socialMediaIcon?.image ? (
        <Image
          className="h-5 w-5 md:h-6 md:w-6"
          src={`${social?.socialMediaIcon?.image}`}
          width={24}
          height={24}
          alt={social?.socialMediaIcon?.alt ?? "social-media-icon"}
        />
      ) : (
        <SocialIcons social={social?.socialMedia as Socials} />
      )}
    </Link>
  );
}

export { Footer_E };
