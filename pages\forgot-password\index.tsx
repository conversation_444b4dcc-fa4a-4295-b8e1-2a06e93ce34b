import { SvgSpinners90Ring } from "@/components/common/icons";
import { Button } from "@/components/ui/shadcn-button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useResetPasswordMutation } from "@/queries/user-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Head from "next/head";
import { useNavigationQuery } from "@/queries/component-queries";
import { useFooterQuery } from "@/queries/component-queries";
import { Footer } from "@/components/sections/footer";
import { Navigation } from "@/components/sections/navigation";
import React from "react";

export const forgotPasswordSchema = z.object({
  email: z.string().email({ message: "Invalid email" }),
});

type ResetPasswordForm = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPassword() {
  const { toast } = useToast();
  const resetPasswordMutation = useResetPasswordMutation();
  const resetPasswordForm = useForm<ResetPasswordForm>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    }
  });
  const navigationData = useNavigationQuery();
  const footerData = useFooterQuery();

  const resetPassword = async (data: ResetPasswordForm) => {
    await resetPasswordMutation.mutateAsync(data.email)
      .then(() => {
        toast({
          title: "Password reset email sent",
          description: "Please check your email(if it exist) for the password reset link.",
          variant: "success",
          duration: 3000,
        });
        resetPasswordForm.reset();
      })
      .catch(e => {
        toast({
          title: "Something went wrong, please try again later.",
          description: e.message,
          variant: "destructive",
          duration: 3000,
        });
      });
  }

  return <React.Fragment>
    {navigationData.data && (
      <div className="sticky top-0 left-0 w-full h-full z-50">
        <Navigation data={navigationData.data?.data} />
      </div>
    )}
    <Head>
      <title>Forgot Password</title>
      <meta name="description" content="Forgot your password? Enter your email to reset it." />
    </Head>
    <section className="relative w-full py-40">
      <div className="w-fit h-full mx-auto max-w-6xl grid place-content-center border rounded-sm shadow-lg px-12 py-10">
        <div className="flex flex-col items-center justify-center gap-2">
          <h1 className="text-2xl font-medium">Forgot password</h1>
          <p className="text-base">We will send you a link to your email to set your new password.</p>
        </div>
        <Form {...resetPasswordForm}>
          <form onSubmit={resetPasswordForm.handleSubmit(resetPassword)} className="w-full h-full flex flex-col gap-4">
            <FormField control={resetPasswordForm.control} name="email" render={({ field }) => (
              <FormItem>
                <FormLabel aria-required>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )} />
            <div className="w-full h-full flex flex-col items-end">
              <Button
                type="submit"
                size="lg"
                className="w-full bg-primary"
                disabled={resetPasswordForm.formState.isSubmitting}
                aria-disabled={resetPasswordForm.formState.isSubmitting}
              >
                <span>
                  Submit
                </span>
                {
                  resetPasswordForm.formState.isSubmitting && (
                    <SvgSpinners90Ring />
                  )
                }
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </section>
    {footerData.data && (
      <div className="w-full h-full">
        <Footer data={footerData.data?.data} />
      </div>
    )}
  </React.Fragment>
}
