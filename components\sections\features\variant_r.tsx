import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button } from "components/ui";
import { FaArrowRightLong } from "react-icons/fa6";

export default function Features_R({
  caption,
  title,
  description,
  featuredItems,
  primaryButton,
  secondaryButton,
  quickForms,
}: FeaturesProps) {
  const [activeIndex, setActiveIndex] = React.useState(0);

  return (
    <Section className="py-20">
      <Container maxWidth={1280}>
        <Flex
          align="start"
          justify="center"
          className="w-full flex-col lg:flex-row"
        >
          <div className="w-full flex flex-col gap-x-5 items-center lg:items-start gap-6 mb-10 lg:mb-8">
            <div className="w-full text-left max-w-xl">
              <CaptionAndTitleSection
                caption={caption}
                title={title}
                description={description}
              />
            </div>
            <div className="mt-4 lg:mt-0 text-right">
              <Buttons
                primaryButton={primaryButton}
                secondaryButton={secondaryButton}
              />
            </div>
          </div>

          <div className="w-full flex flex-col lg:flex-col gap-10  lg:items-start items-center ">
            {/* <div className="w-full flex flex-col">
              <FeatureItems features={featuredItems} />
            </div> */}

            <div className="w-full flex flex-col items-center gap-8 md:gap-4 ">
              <div className="w-full flex flex-col lg:items-start items-center max-w-xl">
                {quickForms?.title && (
                  <Heading
                    fontSize="3xl"
                    type="h2"
                    className="mb-4 text-primary"
                  >
                    {quickForms?.title}
                  </Heading>
                )}

                {quickForms?.description && (
                  <Text className=" text-center lg:text-left">
                    {quickForms?.description}
                  </Text>
                )}
              </div>

              <div className="w-full flex flex-col xs:flex-row gap-4">
                {quickForms?.quickLinks &&
                  quickForms?.quickLinks?.map((form, idx) => (
                    <div className="w-full p-4 md:w-1/2 bg-white rounded-md">
                      <div className="flex flex-col h-full">
                        <div className="flex flex-col h-full">
                          <div className="flex-1">
                            <Text
                              fontSize="xl"
                              weight="bold"
                              className="text-primary mb-3 md:text-left text-center"
                            >
                              {form.title}
                            </Text>
                            <Text muted className="text-sm text-gray-700">
                              {form.description}
                            </Text>
                          </div>

                          {form?.primaryButton?.label ? (
                            <div className="pt-6 ">
                              <Button
                                as="link"
                                link={form?.primaryButton}
                                ariaLabel={
                                  form?.primaryButton?.ariaLabel ??
                                  form?.primaryButton?.label
                                }
                                // size="lg"
                                // className="text-white bg-primary rounded-full w-max py-2 px-4 hover:bg-primary/80  flex items-center space-x-2 transition-all duration-200 hover:scale-110 origin-left"
                                variant="maxtonPrimary"
                              >
                                <span>{form?.primaryButton?.label}</span>
                                <FaArrowRightLong />
                              </Button>
                            </div>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {title && (
        <Heading
          fontSize="3xl"
          type="h2"
          className="!text-primary mb-4 text-center lg:text-left"
        >
          {title}
        </Heading>
      )}
      {caption && (
        <Text fontSize="base" className="text-center lg:text-left">
          {caption}
        </Text>
      )}
    </>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="flex items-center justify-center gap-2 flex-row"
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="maxtonPrimary"
          // size="lg"
          // className="bg-primary text-white  border-2 border-primary hover:border-primary/0 hover:bg-primary/80 rounded-full px-8 py-3"
        >
          {primaryButton.label}
        </Button>
      ) : null}
      {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="maxtonSecondary"
          // className="bg-white-50 border-2 border-primary hover:border-primary/80 text-primary rounded-full px-8 py-3"
        >
          <span>{secondaryButton.label}</span>
          {/* <FaArrowRightLong className="animate-blink" /> */}
        </Button>
      ) : null}
    </Flex>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <Flex wrap justify="center" className="-mx-4">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index + 1} />
      ))}
    </Flex>
  );
}

function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  return (
    <div className="w-full p-2 md:w-1/2 relative">
      <Card
        className="h-full md:h-[200px] px-6 py-12 bg-white text-left lg:mb-0"
        borderRadius="md"
      >
        <div className="absolute top-4 left-10 text-7xl font-bold text-secondary opacity-10">
          {index < 10 ? `0${index}` : index}
        </div>

        <Text fontSize="xl" weight="bold" className="px-0 mb-4 text-gray-800">
          {feature.title}
        </Text>
        <Text muted className="px-0 !text-sm !text-gray-700">
          {feature.description}
        </Text>
      </Card>
    </div>
  );
}

export { Features_R };
