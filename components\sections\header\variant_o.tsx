import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, A11y } from "swiper/modules";
import { ButtonProps, HeaderProps } from ".";
import { FaArrowRightLong } from "react-icons/fa6";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

export default function Header_O({
  title,
  subtitle,
  description,
  primaryButton,
  secondaryButton,
  images,
}: HeaderProps): JSX.Element {
  const [activeIndex, setActiveIndex] = useState(0);
  const [prevImage, setPrevImage] = useState(images?.[0]?.image);
  const [currentImage, setCurrentImage] = useState(images?.[0]?.image);

  useEffect(() => {
    if (!images || images.length === 0) return;

    const interval = setInterval(() => {
      setPrevImage(currentImage);
      setCurrentImage(images[(activeIndex + 1) % images.length]?.image);
      setActiveIndex((prev) => (prev + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [images, activeIndex, currentImage]);
  console.log(activeIndex);

  return (
    <Section className="relative py-20 lg:py-24">
      <div
        className="absolute inset-0 transition-all duration-300 ease-in-out"
        style={{
          backgroundImage: `linear-gradient(rgba(1, 84, 162, 0.1), rgba(1, 84, 162, 0.1)), url(${prevImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          filter: "blur(2px)", // Smooths out the transition
          opacity: 1,
        }}
      ></div>

      <div
        className="absolute inset-0 transition-all duration-300 ease-in-out"
        style={{
          backgroundImage: `linear-gradient(rgba(1, 84, 162, 0.1) .5%, rgba(1, 84, 162, 0.1)), url(${currentImage})`,
          backgroundSize: "center",
          backgroundPosition: "center",
          opacity: 1,
        }}
      ></div>

      {/* Content */}
      <Container maxWidth={1280} className="relative z-10 h-full">
        <Container
          className="w-full flex justify-start flex-col items-center !px-0 !mx-0"
          maxWidth="2xl"
        >
          <TitleAndDescription
            title={title}
            subtitle={subtitle}
            description={description}
          />
          <Buttons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </Container>

        {images && images.length > 0 && (
          <div className="absolute -bottom-24 flex items-center justify-center lg:-bottom-28 left-1/2 -translate-x-1/2 md:gap-3 gap-2 mb-8">
            {images.map((_, index) => (
              <button
                key={index}
                className={` rounded-full transition-all duration-300 ${
                  index === activeIndex
                    ? "bg-transparent border-4 border-primary w-4 h-4"
                    : "bg-transparent border border-white w-2 h-2"
                }`}
                onClick={() => {
                  setPrevImage(currentImage);
                  setCurrentImage(images[index]?.image);
                  setActiveIndex(index);
                }}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  subtitle,
  description,
}: {
  title?: string;
  subtitle?: string;
  description?: string;
}) {
  return (
    <React.Fragment>
      {subtitle && (
        <Text className="md:mb-4 mb-2 text-gray-100 textShadow uppercase w-full text-sm md:text-lg">
          {subtitle}
        </Text>
      )}
      {title ? (
        <Heading
          className="mb-5 text-white !leading-tight textShadow uppercase md:text-5xl lg:text-5xl text-3xl"
          type="h1"
        >
          {title}
        </Heading>
      ) : null}
    </React.Fragment>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="flex items-center justify-center gap-4 flex-row"
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="solid"
          size="lg"
          className="bg-primary  hover:bg-primary/50 text-white rounded-full px-8 py-3"
        >
          {primaryButton.label}
        </Button>
      ) : null}
      {/* {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="solid"
          className="text-white rounded-full px-6 py-3 flex items-center gap-2 animate-blink"
        >
          <span>{secondaryButton.label}</span>
          <FaArrowRightLong className="animate-blink" />
        </Button>
      ) : null} */}
    </Flex>
  );
}

function ImageSlider({
  mainImage,
  images,
  onSlideChange,
}: {
  mainImage?: { image?: string | any; alt?: string };
  images?: any[];
  onSlideChange: (index: number) => void;
}) {
  if (!images?.length) return null;

  return (
    <div className="w-full">
      <Swiper
        modules={[Navigation, Pagination, A11y]}
        spaceBetween={20}
        slidesPerView={1}
        navigation
        pagination={{ clickable: true }}
        className="rounded-md"
        onSlideChange={(swiper) => onSlideChange(swiper.activeIndex)}
      >
        {images.map((img, index) => (
          <SwiperSlide key={index}>
            <Image
              alt={img.alt ?? `header-image-${index + 1}`}
              className="rounded-md aspect-square object-cover"
              height={500}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 40vw"
              src={`${img?.image}`}
              style={{ objectFit: "cover" }}
              width={500}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}

export { Header_O };
