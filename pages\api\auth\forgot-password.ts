import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import createForgotPasswordTemplate from "@/lib/mailer/templates/forgot-password";
import { matchRoute } from "@/middlewares/match-route";
import { forgotPasswordSchema } from "@/pages/forgot-password";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";

export default matchRoute({
    POST: handler
});

async function handler(req: NextApiRequest, res: NextApiResponse) {
    const data = forgotPasswordSchema.safeParse(req.body);

    if (data.error) {
        return res.status(400).json({
            error: data.error.issues[0].message
        });
    }

    const { email } = data.data;
    const supabaseAdminClient = createSupabaseAdminClient();

    const user = await supabaseAdminClient
        .schema("public")
        .from("users")
        .select("id")
        .eq("email", email)
        .single();

    if (user.error) {
        console.log(user.error);
        return res.status(400).json({
            error: "Email not found."
        });
    }

    const password_reset = await supabaseAdminClient
        .from("password_resets")
        .insert({
            user_id: user.data.id,
        })
        .select("token")
        .single();

    if (password_reset.error) {
        return res.status(400).json({
            error: "Failed to create password reset."
        });
    }

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    const resetPasswordTemplate = createForgotPasswordTemplate(email, password_reset.data.token);

    try {
        await sendEmail(mailer, resetPasswordTemplate);
    } catch (e) {
        // DELETE password reset record
        await supabaseAdminClient
            .from("password_resets")
            .delete()
            .eq("token", password_reset.data.token);

        return res.status(400).json({
            error: "Failed to send email."
        });
    }

    return res.status(200).json({
        success: true,
        message: "Password reset email sent.",
    });
}