import { SvgSpinners90Ring } from "@/components/common/icons";
import AdminLayout from "@/components/features/admin/layout";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { formatId } from "@/lib/utils/order-helpers";
import { UpdateUserStatusRequest } from "@/pages/api/users/[slug]/status";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
  useAddCustomerCategoryMutation,
  useCreateCustomerGroupMutation,
  useGetGroupsQuery,
  useGetPendingUsersQuery,
  useUpdateUserStatusMutation,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { UserStatus } from "@/supabase/types";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { Check, Eye, X, XCircle } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import React, { useEffect, useState } from "react";

// Type for tracking approval operations
type ApprovalOperation = "status" | "group" | "category";

export default function PendingCustomers() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "created_at",
      desc: true, // Newest first
    },
  ]);
  const [rowSelection, setRowSelection] = useState({});

  const token = useAuthStore((state) => state.token);
  const { data, isLoading, isError, refetch } = useGetPendingUsersQuery(
    page,
    pageSize,
    token
  );

  const pendingCustomers = data?.data ?? [];
  const totalItems = data?.total ?? 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  const columns: ColumnDef<PublicUserWithCustomer>[] = [
    {
      header: "Company Name",
      accessorKey: "companyName",
      cell: ({ row }) => {
        const customer = row.original;
        return (
          <div className="flex items-center gap-2">
            {customer.customer_data?.[0]?.company_name.trim()
              ? customer.customer_data?.[0]?.company_name
              : "N/A"}
          </div>
        );
      },
    },
    {
      id: "name",
      header: "Name",
      accessorFn: (row) => `${row.first_name} ${row.last_name}`,
      cell: ({ row }) => {
        const customer = row.original;

        return (
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarFallback>{customer.first_name?.charAt(0)}</AvatarFallback>
            </Avatar>
            {customer.first_name} {customer.last_name}
          </div>
        );
      },
    },
    {
      header: "Email",
      accessorKey: "email",
      cell: ({ row }) => {
        const customer = row.original;
        return <div className="flex items-center gap-2">{customer.email}</div>;
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Requested At" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("created_at") as string;
        const date = new Date(createdAt);
        return (
          <div>
            {date.toLocaleDateString("en-US", {
              year: "numeric",
              month: "short",
              day: "numeric",
              hour: "numeric",
              minute: "numeric",
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "updated_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Last Updated" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("updated_at") as string;
        const date = new Date(createdAt);
        return (
          <div>
            {date.toLocaleDateString("en-US", {
              year: "numeric",
              month: "short",
              day: "numeric",
              hour: "numeric",
              minute: "numeric",
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      header: "Actions",
      accessorKey: "actions",
      cell: CustomerActionButtons,
    },
  ];

  const table = useReactTable({
    data: pendingCustomers,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize,
        });
        setPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1,
        pageSize,
      },
    },
    manualPagination: true,
    pageCount: totalPages,
  });

  return (
    <AdminLayout>
      <Head>
        <title>Customers | Pending Approvals</title>
      </Head>
      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-4xl font-bold tracking-tight">
              Manage Pending Approvals
            </h2>
            <p className="text-zinc-500 text-sm">
              Manage pending customer accounts and approve or reject them.
            </p>
          </div>
          <Button variant="outline" className="w-full md:w-auto" asChild>
            <Link href="/admin/customers">Back to All Customers</Link>
          </Button>
        </div>
        <div className="relative w-full h-full flex flex-col gap-12 pt-5">
          {isError ? (
            <div className="flex flex-col items-center justify-center py-10">
              <XCircle className="h-14 w-14 text-red-500 my-4" />
              <h3 className="text-xl font-semibold">
                Error loading pending customers
              </h3>
              <p className="text-gray-500 mt-2">Please try again later</p>
            </div>
          ) : null}
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <>
              <DataTable
                data={pendingCustomers}
                columns={columns}
                table={table}
                renderToolbar={(table) => (
                  <DataTableToolbar table={table}>
                    <DataTableFilter
                      table={table}
                      column="name"
                      placeholder="Search pending customers..."
                      className="max-w-md rounded-full"
                    />
                  </DataTableToolbar>
                )}
              />
            </>
          )}
          {pendingCustomers.length > 0 && (
            <div className="flex items-center justify-end py-4">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((prev) => Math.max(1, prev - 1))}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <div className="text-sm">
                  Page {page} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}

function CustomerStatusBadge({ status }: { status: UserStatus }) {
  const variant =
    status === "approved"
      ? "outline"
      : status === "pending"
        ? "pending"
        : "destructive";

  return (
    <Badge variant={variant} className="uppercase">
      {status}
    </Badge>
  );
}

function CustomerActionButtons({ row }: { row: Row<PublicUserWithCustomer> }) {
  const user = row.original;
  const customerData = user.customer_data?.[0];
  const userId = user.id;
  const shortId = formatId(userId);
  const businessDetails = user.business_details?.at(0);
  const maxtonAccount =
    businessDetails?.maxton_account?.trim();

  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showViewDetailsDialog, setShowViewDetailsDialog] = useState(false);
  const [isRejected, setIsRejected] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);
  const [selectedCategoryType, setSelectedCategoryType] = useState<"all_products" | "support_products" | null>(null);

  const [rejectionNotes, setRejectionNotes] = useState("");
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const token = useAuthStore((state) => state.token);
  const updateUserStatusMutation = useUpdateUserStatusMutation(user.id, token);
  const createCustomerGroupMutation = useCreateCustomerGroupMutation(token);
  const addCustomerCategoryMutation = useAddCustomerCategoryMutation(token);
  const { refetch } = useGetPendingUsersQuery(1, 10, token);
  const { data: groupData } = useGetGroupsQuery(1, 999, token);

  // Validation function to check if all required fields are filled
  const validateApprovalFields = (): string[] => {
    const errors: string[] = [];

    if (!selectedGroupId) {
      errors.push("User group must be selected");
    }

    if (!selectedCategoryType) {
      errors.push("Category assignment must be selected");
    }

    return errors;
  };

  // Check if approval is ready (all fields filled)
  const isApprovalReady = selectedGroupId && selectedCategoryType;

  const handleApproveUser = async () => {
    try {
      const customerId = user?.customer_data?.[0]?.id;
      const errors = validateApprovalFields();

      if (!customerId) {
        toast({
          title: "Failed",
          description: "Customer Account# is required to approve the user.",
          variant: "destructive",
        });
        return;
      }

      if (errors.length > 0) {
        setValidationErrors(errors);
        toast({
          title: "Validation Failed",
          description: "Please complete all required fields before approving.",
          variant: "destructive",
        });
        return;
      }

      // Clear any previous validation errors
      setValidationErrors([]);

      // Create an array of promises to execute all operations
      const promises = [
        updateUserStatusMutation.mutateAsync({ status: "approved" } as UpdateUserStatusRequest),
        createCustomerGroupMutation.mutateAsync({
          customer_ids: [customerId],
          group_id: selectedGroupId!,
        }),
        addCustomerCategoryMutation.mutateAsync({
          customerId,
          categoryType: selectedCategoryType!,
        })
      ];

      // Execute all operations simultaneously
      await Promise.all(promises);

      toast({
        title: "Success",
        description: "Customer registration completed successfully.",
        variant: "default",
      });

      setShowApproveDialog(false);
      setIsRejected(false);

      // Reset form state
      setSelectedGroupId(null);
      setSelectedCategoryType(null);
      setValidationErrors([]);

    } catch (error: any) {
      console.error("Error approving user:", error);
      toast({
        title: "Approval Failed",
        description: error.message || "An error occurred during the approval process. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRejectUser = () => {
    updateUserStatusMutation.mutate({
      status: "rejected",
      notes: rejectionNotes.trim() || undefined
    } as UpdateUserStatusRequest);
    setIsRejected(true);
  };

  useEffect(
    function showToast() {
      const message = isRejected
        ? "Customer request rejected"
        : "Customer approved successfully";
      const errorMessage = isRejected
        ? "Failed to reject customer"
        : "Failed to approve customer";

      if (updateUserStatusMutation.isSuccess) {
        toast({
          title: "Success",
          description: message,
          variant: "default",
        });
        refetch();
        setShowRejectDialog(false);
        setShowApproveDialog(false);
        if (isRejected) {
          setRejectionNotes("");
        }
      }

      if (updateUserStatusMutation.isError) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        setShowRejectDialog(false);
        setShowApproveDialog(false);
      }
    },
    [
      updateUserStatusMutation.isSuccess,
      updateUserStatusMutation.isError,
      refetch,
      isRejected,
    ]
  );



  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        className="h-8 px-3"
        onClick={() => setShowViewDetailsDialog(true)}
      >
        <Eye className="h-4 w-4 mr-2" />
        View Details
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="h-8 px-3 text-green-500"
        onClick={() => setShowApproveDialog(true)}
      >
        <Check className="h-4 w-4 mr-2" />
        Approve
      </Button>

      <Dialog open={showViewDetailsDialog} onOpenChange={setShowViewDetailsDialog}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Customer Details</DialogTitle>
            <DialogDescription>
              Detailed information about {user.first_name} {user.last_name}
            </DialogDescription>
          </DialogHeader>
          <div className="gap-6 py-4">
            <div className="gap-6 py-4">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Personal Information</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Customer Account#</div>
                  <div className="text-sm text-gray-500">{maxtonAccount || shortId}</div>

                  <div className="text-sm font-medium">Name</div>
                  <div className="text-sm text-gray-500">
                    {user.first_name} {user.last_name}
                  </div>

                  <div className="text-sm font-medium">Email</div>
                  <div className="text-sm text-gray-500">{user.email}</div>

                  <div className="text-sm font-medium">Role</div>
                  <div className="text-sm text-gray-500">{user.role}</div>

                  <div className="text-sm font-medium">Status</div>
                  <div className="text-sm text-gray-500">
                    <CustomerStatusBadge status={user.status} />
                  </div>
                  <div className="text-sm font-medium">Created At</div>
                  <div className="text-sm text-gray-500">
                    {new Date(user.created_at).toLocaleDateString()}
                  </div>
                  <div className="text-sm font-medium">Notes</div>
                  <div className="text-sm text-gray-500">{user.notes || "N/A"}</div>
                </div>
              </div>
            </div>

            {/* Business Details */}
            <div className="space-y-4 mt-4 border-t pt-4">
              <h3 className="text-lg font-semibold">Business Details</h3>
              {businessDetails ? (
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Website</div>
                  <div className="text-sm text-gray-500">
                    {businessDetails?.website ? (
                      <a
                        href={businessDetails?.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        {businessDetails?.website}
                      </a>
                    ) : (
                      "N/A"
                    )}
                  </div>

                  <div className="text-sm font-medium">Buyer Name</div>
                  <div className="text-sm text-gray-500">
                    {businessDetails?.buyer_name || "N/A"}
                  </div>

                  {Object.entries(businessDetails)
                    .filter(
                      ([key]) =>
                        ![
                          "id",
                          "user_id",
                          "created_at",
                          "website",
                          "buyer_name",
                        ].includes(key)
                    )
                    .map(([key, value]) => (
                      <React.Fragment key={key}>
                        <div className="text-sm font-medium">
                          {key
                            .split("_")
                            .map(
                              (word) => word.charAt(0).toUpperCase() + word.slice(1)
                            )
                            .join(" ")}
                        </div>
                        <div className="text-sm text-gray-500">
                          {String(value || "N/A")}
                        </div>
                      </React.Fragment>
                    ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">
                  No business details available
                </div>
              )}
            </div>

            {/* Customer Information */}
            {customerData && (
              <div className="space-y-4 mt-4 border-t pt-4">
                <h3 className="text-lg font-semibold">Customer Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Company Name</div>
                    <div className="text-sm text-gray-500">
                      {customerData.company_name || "N/A"}
                    </div>

                    <div className="text-sm font-medium">Contact Name</div>
                    <div className="text-sm text-gray-500">
                      {customerData.primary_contact_name || "N/A"}
                    </div>

                    <div className="text-sm font-medium">Phone</div>
                    <div className="text-sm text-gray-500">
                      {user.phone || "N/A"}
                    </div>

                    <div className="text-sm font-medium">Customer Number</div>
                    <div className="text-sm text-gray-500">
                      {customerData.customer_number || "N/A"}
                    </div>

                    <div className="text-sm font-medium">Credit Limit</div>
                    <div className="text-sm text-gray-500">
                      {customerData.credit_limit !== null
                        ? `$${customerData.credit_limit.toFixed(2)}`
                        : "N/A"}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Company Website</div>
                    <div className="text-sm text-gray-500">
                      {customerData.company_website ? (
                        <a
                          href={customerData.company_website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {customerData.company_website}
                        </a>
                      ) : (
                        "N/A"
                      )}
                    </div>

                    <div className="text-sm font-medium">Customer Role</div>
                    <div className="text-sm text-gray-500">
                      {customerData.role || "N/A"}
                    </div>

                    <div className="text-sm font-medium">Status</div>
                    <div className="text-sm text-gray-500">
                      {customerData.status && (
                        <CustomerStatusBadge
                          status={customerData.status as UserStatus}
                        />
                      )}
                    </div>

                    <div className="text-sm font-medium">Group</div>
                    <div className="text-sm text-gray-500">
                      {customerData.group_data?.data?.name || "None"}
                    </div>
                  </div>
                </div>

                {customerData.shipping_notes && (
                  <div className="mt-4">
                    <div className="text-sm font-medium">Shipping Notes</div>
                    <div className="text-sm text-gray-500 mt-1 p-2 border rounded bg-gray-50">
                      {customerData.shipping_notes}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Complete Customer Registration</DialogTitle>
            <DialogDescription>
              Complete the registration for {user.first_name} {user.last_name} by assigning a user group and category access.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Group Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Select a Group <span className="text-red-500">*</span>
              </Label>
              <Select
                value={selectedGroupId || ""}
                onValueChange={(value) => {
                  setSelectedGroupId(value);
                  // Clear validation errors when user makes a selection
                  if (validationErrors.length > 0) {
                    setValidationErrors([]);
                  }
                }}
              >
                <SelectTrigger className={`${!selectedGroupId && validationErrors.some(e => e.includes('group')) ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select user group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Available Groups</SelectLabel>
                    {groupData?.groups && groupData.groups.length > 0 ? (
                      groupData.groups
                        .filter(group => group.name !== "VISITOR")
                        .map((group) => (
                          <SelectItem key={group.id} value={group.id}>
                            {group.name}
                          </SelectItem>
                        ))
                    ) : (
                      <SelectItem value="no-groups" disabled>
                        No groups available
                      </SelectItem>
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* Category Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Select a Category <span className="text-red-500">*</span>
              </Label>
              <Select
                value={selectedCategoryType || ""}
                onValueChange={(value) => {
                  setSelectedCategoryType(value as "all_products" | "support_products");
                  // Clear validation errors when user makes a selection
                  if (validationErrors.length > 0) {
                    setValidationErrors([]);
                  }
                }}
              >
                <SelectTrigger className={`${!selectedCategoryType && validationErrors.some(e => e.includes('Category')) ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select category access">
                    {selectedCategoryType === "all_products" && "All Products"}
                    {selectedCategoryType === "support_products" && "Support Products"}
                    {!selectedCategoryType && "Select category access"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Category Types</SelectLabel>
                    <SelectItem value="all_products">All Products</SelectItem>
                    <SelectItem value="support_products">Support Products</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* Validation Error Messages */}
            {validationErrors.length > 0 && (
              <div className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Summary */}
            {/* {isApprovalReady && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                <h4 className="font-medium text-blue-900 mb-2">Registration Summary:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>Status:</strong> Approved</li>
                  <li>• <strong>User Group:</strong> {groupData?.groups?.find(g => g.id === selectedGroupId)?.name}</li>
                  <li>• <strong>Category Access:</strong> {selectedCategoryType === "all_products" ? "All Products" : "Support Products"}</li>
                </ul>
              </div>
            )} */}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowApproveDialog(false);
                setSelectedGroupId(null);
                setSelectedCategoryType(null);
                setValidationErrors([]);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              type="button"
              onClick={handleApproveUser}
              disabled={
                updateUserStatusMutation.isPending ||
                createCustomerGroupMutation.isPending ||
                addCustomerCategoryMutation.isPending ||
                !isApprovalReady
              }
            >
              {updateUserStatusMutation.isPending ||
                createCustomerGroupMutation.isPending ||
                addCustomerCategoryMutation.isPending
                ? "Processing..."
                : "Approve"}
              {updateUserStatusMutation.isPending ||
                createCustomerGroupMutation.isPending ||
                addCustomerCategoryMutation.isPending ? (
                <SvgSpinners90Ring className="h-4 w-4 ml-2" />
              ) : null}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Button
        variant="outline"
        size="sm"
        className="h-8 px-3 text-red-500"
        onClick={() => setShowRejectDialog(true)}
        type="button"
      >
        <X className="h-4 w-4 mr-2" />
        Reject
      </Button>

      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Customer</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject {user.first_name} {user.last_name}
              ? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="rejection-notes">Rejection Notes (Optional)</Label>
              <Textarea
                id="rejection-notes"
                placeholder="Enter reason for rejection..."
                value={rejectionNotes}
                onChange={(e) => setRejectionNotes(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRejectDialog(false);
                setRejectionNotes("");
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectUser}
              disabled={updateUserStatusMutation.isPending}
              type="button"
            >
              {updateUserStatusMutation.isPending
                ? "Rejecting..."
                : "Confirm Rejection"}
              {updateUserStatusMutation.isPending ? (
                <SvgSpinners90Ring className="h-4 w-4 ml-2" />
              ) : null}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
