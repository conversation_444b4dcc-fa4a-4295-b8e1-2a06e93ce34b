import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { PortableText } from "@portabletext/react";
import React, { useState, useEffect } from "react";
import { CiSearch } from "react-icons/ci";
import { Input } from "@stackshift-ui/input";
import { FaDownload } from "react-icons/fa6";

import { PortfolioProps } from ".";
import { PortableTextBlock } from "@sanity/types";
import { textComponentBlockStyling } from "../features/variant_t";
import ValveCalculator from "../../calculator/sideCalculator";

// Define the correct interface for the actual data structure used in this component
interface CustomLabeledRoute {
  _type?: string;
  label?: string;
  linkExternal?: string;
  linkTarget?: string;
  linkType?: string;
  linkInternal?: any;
}

interface CustomPortfoliosWithCategories {
  category?: string | null;
  content?: CustomContent[] | null;
  primaryButton?: CustomLabeledRoute | null;
  zipFile?: {
    asset?: {
      url?: string;
    };
  };
  _key?: string | null;
  _type?: string | null;
}

interface CustomContent {
  description?: string | null;
  subtitle?: string | null;
  title?: string | null;
  firstColumn?: any[];
  secondColumn?: any[];
  primaryButton?: CustomLabeledRoute | null;
  _key?: string;
  _type?: string;
}

// Define a proper type for props received by custom block components
interface CustomComponentProps {
  children?: React.ReactNode;
  value?: any;
  isInline?: boolean;
  [key: string]: any;
}

// Custom component to render a checklist item - Update with text-base class
const CheckableListItem = ({
  children,
  index,
  isChecked,
  onToggle,
}: {
  children: React.ReactNode;
  index: number;
  isChecked: boolean;
  onToggle: (index: number) => void;
}) => {
  const textColorClass = isChecked ? "text-gray-400" : "text-gray-900";

  return (
    <li
      className={`mb-3 leading-loose flex items-start gap-1 ${textColorClass}`}
    >
      <div className="flex items-center mt-[7px] mr-3">
        <input
          type="checkbox"
          checked={isChecked}
          onChange={() => onToggle(index)}
          className="form-checkbox h-6 w-6 text-primary rounded border-gray-300 focus:ring-primary"
        />
      </div>
      <div className={`text-base ${textColorClass}`}>{children}</div>
    </li>
  );
};

const customComponents = {
  list: {
    bullet: (props: CustomComponentProps) => {
      return (
        <ul className="pl-5 mb-3 leading-loose text-gray-900 list-none">
          {props.children}
        </ul>
      );
    },
    number: ({ children }: CustomComponentProps) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children, index }: CustomComponentProps) => {
      // Add a numbered circle for bullet list items
      return (
        <li className="mb-3 leading-loose text-gray-900 flex items-start gap-1">
          <span className="text-white font-bold bg-primary rounded-full w-7 h-7 shrink-0 flex items-center justify-center mt-[2px] mr-3">
            {(index || 0) + 1}
          </span>
          <div>{children}</div>
        </li>
      );
    },
    number: ({ children, index }: CustomComponentProps) => {
      // For number lists, maintain the same style
      return (
        <li className="mb-3 leading-loose text-gray-900 flex items-start gap-1">
          <span className="text-white font-bold bg-primary rounded-full w-7 h-7 shrink-0 flex items-center justify-center mt-[2px] mr-3">
            {(index || 0) + 1}
          </span>
          <div>{children}</div>
        </li>
      );
    },
  },
  block: {
    h1: ({ children }: CustomComponentProps) => (
      <h1 className="mb-6 leading-loose text-gray-900 text-7xl">{children}</h1>
    ),
    h2: ({ children }: CustomComponentProps) => (
      <h2 className="mb-4 text-5xl text-primary">{children}</h2>
    ),
    h3: ({ children }: CustomComponentProps) => (
      <h3 className="mb-4 text-2xl text-gray-800">{children}</h3>
    ),
    h4: ({ children }: CustomComponentProps) => (
      <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
    ),
    h5: ({ children }: CustomComponentProps) => (
      <h5 className="mb-6 text-xl leading-loose text-gray-900">{children}</h5>
    ),
    h6: ({ children }: CustomComponentProps) => (
      <h6 className="mb-6 text-xl leading-loose text-gray-900">{children}</h6>
    ),
    normal: ({ children }: CustomComponentProps) => (
      <p className="mb-5 font-body text-lg text-gray-800 leading-loose">
        {children}
      </p>
    ),
    blockquote: ({ children }: CustomComponentProps) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }: CustomComponentProps) => {
    return (
      <pre data-language={value?.language}>
        <code>{value?.code}</code>
      </pre>
    );
  },
  marks: {
    strong: ({ children }: CustomComponentProps) => (
      <strong className="font-bold">{children}</strong>
    ),
    em: ({ children }: CustomComponentProps) => (
      <em className="italic">{children}</em>
    ),
    code: ({ children }: CustomComponentProps) => <code>{children}</code>,
    link: ({ children, value }: CustomComponentProps) => {
      const target = (value?.href || "").startsWith("http")
        ? "_blank"
        : undefined;
      return (
        <a
          href={value?.href}
          target={target}
          rel={target === "_blank" ? "noopener noreferrer" : undefined}
          className="text-primary hover:text-primary/70 border-b border-primary"
        >
          {children}
        </a>
      );
    },
  },
  types: {
    addTable: ({ value }: CustomComponentProps) => {
      if (!value?.columns || !value?.rows) {
        console.error("Missing table data:", value);
        return null;
      }

      // Get the number of columns from the header
      const columnCount = value.columns.length;

      return (
        <div className="overflow-x-auto my-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 table-fixed">
            <thead className="bg-gray-50">
              <tr>
                {value.columns.map(
                  (column: { title: string }, index: number) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-semibold text-gray-800 uppercase tracking-wider border border-gray-200"
                    >
                      {column.title || "\u00A0"}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {value.rows.map((row: { cells: string[] }, rowIndex: number) => {
                // Create a new array with the correct number of cells
                const normalizedCells = Array(columnCount).fill("");

                // Fill in existing data
                if (row.cells && Array.isArray(row.cells)) {
                  row.cells.forEach((cell, idx) => {
                    if (idx < columnCount) {
                      normalizedCells[idx] = cell;
                    }
                  });
                }

                return (
                  <tr key={rowIndex} className="bg-white">
                    {normalizedCells.map((cell, cellIndex) => {
                      // Ensure cell content is treated as a string and check if it's empty
                      const cellContent = String(cell || "");
                      const isEmpty = !cellContent.trim();

                      return (
                        <td
                          key={cellIndex}
                          className="px-6 py-4 text-xs md:text-sm text-gray-500 bg-white border border-gray-200"
                          style={{
                            minHeight: "40px",
                            height: isEmpty ? "40px" : "auto",
                          }}
                          dangerouslySetInnerHTML={{
                            __html: isEmpty
                              ? "&nbsp;" // Non-breaking space for empty cells
                              : cellContent
                                  .replace(
                                    /\n\n/g,
                                    "<hr class='my-2 border-gray-300' />"
                                  )
                                  .replace(/\n/g, "<br />"),
                          }}
                        />
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      );
    },
  },
};

export default function Portfolio_G({
  caption,
  title,
  portfoliosWithCategory,
  firstColumn,
  hasCalculator,
}: PortfolioProps): React.JSX.Element {
  const [activeCategory, setActiveCategory] = useState<
    string | null | undefined
  >(portfoliosWithCategory?.[0]?.category);
  const [activeContentIndex, setActiveContentIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const contentRef = React.useRef<HTMLDivElement>(null);

  // Cast to our custom type to avoid type errors
  const typedPortfoliosWithCategory = portfoliosWithCategory as unknown as
    | CustomPortfoliosWithCategories[]
    | undefined;

  const activePortfolio = typedPortfoliosWithCategory?.find(
    (portfolio) => portfolio?.category === activeCategory
  );

  // State for the interactive checklist
  const [checkedItems, setCheckedItems] = useState<boolean[]>([]);
  const [visibleSteps, setVisibleSteps] = useState(1);
  const [showHelpMessage, setShowHelpMessage] = useState(false);

  // Search results state
  const [searchResults, setSearchResults] = useState<
    Array<{ categoryIndex: number; contentIndex: number }>
  >([]);

  // Get contents for the active portfolio
  const contents = activePortfolio?.content || [];

  // Reset checklist state when content changes
  useEffect(() => {
    // Find how many list items this content has
    const content = contents[activeContentIndex];
    if (!content) return;

    // Reset checklist state for new content
    setCheckedItems([]);
    setVisibleSteps(1);
    setShowHelpMessage(false);

    // Extract list items to count them
    if (content.firstColumn) {
      // Count the number of list items in firstColumn
      const numberListItems = content.firstColumn.filter(
        (block) => block._type === "block" && block.listItem === "number"
      );

      // Initialize checklist state
      if (numberListItems.length > 0) {
        setCheckedItems(new Array(numberListItems.length).fill(false));
      }
    }
  }, [activeContentIndex, activeCategory, contents]);

  // Filtered results for search
  const filteredResults = React.useMemo(() => {
    if (!searchQuery) return [];

    const results: Array<{ categoryIndex: number; contentIndex: number }> = [];

    typedPortfoliosWithCategory?.forEach((category, categoryIndex) => {
      category?.content?.forEach((content, contentIndex) => {
        // Check content title
        if (content.title?.toLowerCase().includes(searchQuery.toLowerCase())) {
          results.push({ categoryIndex, contentIndex });
        }

        // Check content text for matches (simplified)
        const contentText =
          JSON.stringify(content.firstColumn || "") +
          JSON.stringify(content.secondColumn || "");
        if (contentText.toLowerCase().includes(searchQuery.toLowerCase())) {
          // Only add if not already found by title
          if (
            !results.some(
              (result) =>
                result.categoryIndex === categoryIndex &&
                result.contentIndex === contentIndex
            )
          ) {
            results.push({ categoryIndex, contentIndex });
          }
        }
      });
    });

    return results;
  }, [typedPortfoliosWithCategory, searchQuery]);

  // Update search results when the query changes
  useEffect(() => {
    setSearchResults(filteredResults);

    // If we have results, set the active category and content to the first result
    if (filteredResults.length > 0) {
      const firstResult = filteredResults[0];
      setActiveCategory(
        typedPortfoliosWithCategory?.[firstResult.categoryIndex]?.category
      );
      setActiveContentIndex(firstResult.contentIndex);
    }
  }, [filteredResults, typedPortfoliosWithCategory]);

  // Scroll to content when it changes
  useEffect(() => {
    // Only scroll on initial load or when changing categories
    // Not when clicking content tabs within the same category
    if (contentRef.current && activeContentIndex === 0) {
      // Get the element's position
      const yOffset = -100; // 200px offset to show tabs
      const element = contentRef.current;
      const y =
        element.getBoundingClientRect().top + window.pageYOffset + yOffset;

      // Scroll to the element with offset
      window.scrollTo({
        top: y,
        behavior: "smooth",
      });
    }
  }, [activeCategory]); // Only depend on activeCategory, not activeContentIndex

  // Function to handle checkbox toggling
  const handleCheckboxToggle = (index: number) => {
    const newCheckedItems = [...checkedItems];
    newCheckedItems[index] = !newCheckedItems[index];
    setCheckedItems(newCheckedItems);

    // If checked and not the last step, show the next step
    if (
      newCheckedItems[index] &&
      index === visibleSteps - 1 &&
      index < checkedItems.length - 1
    ) {
      setVisibleSteps(visibleSteps + 1);
    }

    // If all items are checked, show the help message
    if (newCheckedItems.every((item) => item) && newCheckedItems.length > 0) {
      setShowHelpMessage(true);
    } else {
      setShowHelpMessage(false);
    }
  };

  // Create a custom PortableText renderer for checklists
  const checklistRenderer = (content: any) => {
    if (!content || !Array.isArray(content)) return null;

    // Filter out number list items for separate handling
    const numberListItems = content.filter(
      (block) => block._type === "block" && block.listItem === "number"
    );

    // Other content items
    const otherContentItems = content.filter(
      (block) => block._type !== "block" || block.listItem !== "number"
    );

    // Define a custom component set specifically for the checklist items
    const checklistComponents = {
      ...customComponents,
      block: {
        ...customComponents.block,
        normal: ({ children }: CustomComponentProps) => (
          <p className="mb-5 font-body text-base leading-loose">{children}</p>
        ),
      },
    };

    return (
      <>
        {/* Render other content normally */}
        <div className="prose max-w-none">
          <PortableText
            value={otherContentItems}
            components={customComponents}
            onMissingComponent={false}
          />
        </div>

        {/* Render number list items as a checklist with progressive reveal */}
        {numberListItems.length > 0 && (
          <ol className="pl-0 mb-6 list-none">
            {numberListItems.slice(0, visibleSteps).map((item, idx) => (
              <CheckableListItem
                key={idx}
                index={idx}
                isChecked={checkedItems[idx] || false}
                onToggle={handleCheckboxToggle}
              >
                <div className="text-inherit">
                  <PortableText
                    value={[{ ...item, listItem: undefined }]}
                    components={checklistComponents}
                    onMissingComponent={false}
                  />
                </div>
              </CheckableListItem>
            ))}
          </ol>
        )}

        {/* Help message shown when all steps are completed */}
        {showHelpMessage && (
          <div className="mt-8 p-4 bg-gray-100 border-l-4 border-primary">
            <Text className="text-gray-700">
              For additional help please contact Maxton tech support at{" "}
              <a
                href="tel:**************"
                className="text-primary underline hover:text-primary/40"
              >
                **************
              </a>
            </Text>
          </div>
        )}
      </>
    );
  };

  // Build tabs for each content title
  const hasTabs = contents.some((content) => content.title);

  // Check if there's any content at all
  const hasContent =
    contents.length > 0 &&
    contents.some(
      (content) =>
        (content.firstColumn && content.firstColumn.length > 0) ||
        (content.secondColumn && content.secondColumn.length > 0)
    );

  // Function to handle zip file downloads
  const handleDownload = (event: React.MouseEvent) => {
    const zipUrl = activePortfolio?.zipFile?.asset?.url;
    if (zipUrl) {
      event.preventDefault(); // Prevent default link behavior
      const link = document.createElement("a");
      link.href = zipUrl;
      link.setAttribute("download", "");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        <CaptionAndTitleSection title={title} firstColumn={firstColumn} />

        <Flex direction="col" justify="between" className="lg:flex-row">
          <div
            className={`relative w-full  ${
              hasCalculator
                ? "lg:w-[70%] lg:pr-8 lg:border-r border-gray-300"
                : ""
            }`}
          >
            <Flex className="flex-col md:flex-row gap-8 md:gap-16">
              {/* Left sidebar with category selection */}
              <div className="w-full md:w-1/4 lg:w-1/3 max-w-[300px] lg:sticky lg:top-28">
                {/* Search Bar */}
                <Flex className="justify-start my-10">
                  <div className="relative max-w-md w-full">
                    <Input
                      type="text"
                      placeholder="Search content..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full border border-primary/80 rounded-none pr-10 !py-1 focus:border-primary focus:ring-1 focus:ring-primary focus:rounded-none"
                    />
                    <CiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </Flex>

                {/* Category Selection (previously Table of Contents) */}
                <div className="sticky top-24 bg-gray-100 p-6 rounded-lg">
                  <Text
                    fontSize="lg"
                    weight="bold"
                    className="mb-6 !text-gray-900"
                  >
                    Select Adjustment Procedure:
                  </Text>
                  <div className="space-y-2">
                    {typedPortfoliosWithCategory?.map((portfolio, index) => (
                      <Button
                        key={portfolio?._key}
                        variant="ghost"
                        as="button"
                        ariaLabel={
                          portfolio?.category ?? `Category ${index + 1}`
                        }
                        className={`w-full text-left flex justify-between items-center px-3 py-3 !font-semibold rounded-none ${
                          activeCategory === portfolio?.category
                            ? "bg-primary text-white hover:text-white"
                            : "text-gray-700 hover:bg-primary/10"
                        }`}
                        onClick={() => {
                          setActiveCategory(portfolio?.category);
                          setActiveContentIndex(0);
                        }}
                      >
                        <span>{portfolio?.category}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Main content area */}
              <div className="w-full md:w-3/4 lg:w-2/3 py-10" ref={contentRef}>
                {searchQuery && filteredResults.length === 0 ? (
                  <div className="text-center py-8">
                    <Text className="text-gray-500">
                      No results found for "{searchQuery}"
                    </Text>
                  </div>
                ) : (
                  <>
                    {/* Search results notification */}
                    {searchQuery && filteredResults.length > 0 && (
                      <div className="mb-6">
                        <Text className="text-gray-500">
                          Found {filteredResults.length} result
                          {filteredResults.length > 1 ? "s" : ""} for "
                          {searchQuery}"
                        </Text>
                      </div>
                    )}

                    {/* Header with content tabs and download button - only show when we have content */}
                    {hasContent && (
                      <>
                        {/* Download PDF/ZIP button (when we have content) */}
                        <div className="w-full flex flex-col items-end mb-5">
                          {activePortfolio?.primaryButton?.label ? (
                            <Button
                              as="link"
                              link={activePortfolio?.primaryButton}
                              variant="unstyled"
                              ariaLabel="Download PDF"
                              className="relative flex items-center justify-end  gap-2 bg-white-50 border-2 border-primary hover:border-primary/80 text-primary hover:text-white !rounded-none px-4 py-[7px] transition duration-200 overflow-hidden z-10 before:content-[''] before:absolute before:top-0 before:left-0 before:w-0 before:h-full before:bg-[#063a6b] before:transition-all before:duration-300 before:ease-in-out before:z-[-1] hover:before:w-full"
                            >
                              <FaDownload className="text-sm" />
                              <span>
                                {activePortfolio?.primaryButton?.label}
                              </span>
                            </Button>
                          ) : (
                            activePortfolio?.zipFile?.asset?.url && (
                              <Button
                                as="button"
                                onClick={handleDownload}
                                variant="maxtonSecondary"
                                ariaLabel="Download ZIP"
                                // className="flex items-center gap-2 bg-primary !text-white px-4 py-[9px] hover:bg-primary/90 rounded-none"
                              >
                                <FaDownload className="text-sm" />
                                <span>Download Files</span>
                              </Button>
                            )
                          )}
                        </div>
                        {/* Content divider */}
                        <div className="w-full border-b-2 border-primary h-1 mb-1">
                          <div className="h-1 w-10 bg-primary"></div>
                        </div>
                        <div className="flex justify-between items-center mb-10">
                          {/* Content tabs for this category */}
                          {hasTabs ? (
                            <div className="flex flex-wrap">
                              {contents.map((content, index) =>
                                content.title ? (
                                  <Button
                                    key={content._key}
                                    variant="unstyled"
                                    as="button"
                                    ariaLabel={content.title}
                                    className={`px-4 py-[9px] font-medium ${
                                      activeContentIndex === index
                                        ? "bg-primary text-white"
                                        : "text-gray-700 hover:text-primary hover:bg-primary/10 bg-gray-300"
                                    }`}
                                    onClick={() => setActiveContentIndex(index)}
                                  >
                                    {content.title}
                                  </Button>
                                ) : null
                              )}
                            </div>
                          ) : (
                            <Heading
                              fontSize="2xl"
                              type="h2"
                              className="!text-primary"
                            >
                              {activePortfolio?.category}
                            </Heading>
                          )}
                        </div>
                      </>
                    )}

                    {/* Content display */}
                    {searchQuery ? (
                      // Show all matching results when searching
                      <div className="space-y-12">
                        {filteredResults.map((result, idx) => {
                          const category =
                            typedPortfoliosWithCategory?.[result.categoryIndex];
                          const content =
                            category?.content?.[result.contentIndex];
                          if (!content) return null;

                          return (
                            <div
                              key={`search-result-${idx}`}
                              className="mb-8 pb-8 border-b border-gray-200"
                            >
                              <Heading
                                fontSize="xl"
                                type="h3"
                                className="mb-2 !text-primary"
                              >
                                {category?.category}: {content.title}
                              </Heading>
                              <div className="prose max-w-none">
                                {content.firstColumn && (
                                  <PortableText
                                    value={content.firstColumn}
                                    components={customComponents}
                                    onMissingComponent={false}
                                  />
                                )}
                                {content.secondColumn && (
                                  <PortableText
                                    value={content.secondColumn}
                                    components={customComponents}
                                    onMissingComponent={false}
                                  />
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      // Show the selected content when not searching
                      <div className="content-display">
                        {hasContent
                          ? // Display actual content when available
                            contents[activeContentIndex] && (
                              <>
                                {/* Render special checklist for step-by-step instructions */}
                                {contents[activeContentIndex].firstColumn &&
                                  checklistRenderer(
                                    contents[activeContentIndex].firstColumn
                                  )}

                                {/* Second column content (if any) */}
                                {contents[activeContentIndex].secondColumn && (
                                  <div className="prose max-w-none mt-6">
                                    <PortableText
                                      value={
                                        contents[activeContentIndex]
                                          .secondColumn
                                      }
                                      components={customComponents}
                                      onMissingComponent={false}
                                    />
                                  </div>
                                )}
                              </>
                            )
                          : // No content available, show a centered download button
                            (activePortfolio?.primaryButton?.label ||
                              activePortfolio?.zipFile?.asset?.url) && (
                              <div className="flex flex-col items-center justify-center py-20 text-center">
                                <Heading
                                  fontSize="3xl"
                                  type="h2"
                                  className="mb-8 !text-primary"
                                >
                                  {activePortfolio.category}
                                </Heading>
                                <Text className="mb-10 text-lg text-gray-700 max-w-lg">
                                  Download the adjustment procedure document for
                                  complete instructions.
                                </Text>
                                {activePortfolio?.primaryButton?.label ? (
                                  <Button
                                    as="link"
                                    link={activePortfolio.primaryButton}
                                    variant="maxtonPrimary"
                                    ariaLabel="Download PDF"
                                    className="flex items-center gap-3 bg-primary text-white px-8 py-4 text-lg hover:bg-primary/90 transition-colors"
                                  >
                                    <FaDownload className="text-xl" />
                                    <span>
                                      {activePortfolio.primaryButton.label}
                                    </span>
                                  </Button>
                                ) : (
                                  activePortfolio?.zipFile?.asset?.url && (
                                    <Button
                                      as="button"
                                      onClick={handleDownload}
                                      variant="ghost"
                                      ariaLabel="maxtonPrimary"
                                      className="flex items-center gap-3 bg-primary !text-white px-8 py-4 text-lg hover:bg-primary/90 transition-colors rounded-none"
                                    >
                                      <FaDownload className="text-xl" />
                                      <span>Download Files</span>
                                    </Button>
                                  )
                                )}
                              </div>
                            )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </Flex>
          </div>
          {hasCalculator && (
            <div className="w-full lg:w-[30%] mt-8 lg:mt-4 lg:pl-8">
              <ValveCalculator />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
  firstColumn,
}: {
  title?: string;
  firstColumn?: PortableTextBlock[];
}) {
  return (
    <div className="relative lg:-mt-48 w-full lg:w-2/3 lg:pl-10 ml-auto mb-10">
      <div className="w-full lg:bg-white lg:rounded-lg lg:shadow-xl md:p-8">
        {title && (
          <Heading type="h2" className="text-xl lg:text-2xl mb-3">
            {title}
          </Heading>
        )}
        {firstColumn && (
          <div>
            <PortableText
              value={firstColumn}
              components={textComponentBlockStyling}
              onMissingComponent={false}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export { Portfolio_G };
