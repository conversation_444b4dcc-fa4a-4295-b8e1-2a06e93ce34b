import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState } from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, LabeledRoute } from "../../../types";
import { ButtonProps } from "../header";
import { <PERSON>ton, Card, Link } from "components/ui";
import { PortableText } from "@portabletext/react";

import { MyPortableTextComponents } from "types";
import { PortableTextBlock } from "@sanity/types";
import { urlFor } from "lib/sanity";
import { FaArrowRightLong } from "react-icons/fa6";
import Image from "next/image";
import ValveCalculator from "../../calculator/sideCalculator";

const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-900 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 underline"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

const textComponentBlock: MyPortableTextComponents = {
  block: {
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-lg text-gray-900 leading-loose">
          {children}
        </p>
      );
    },
  },
};

export default function Features_V({
  caption,
  title,
  description,
  firstColumn,
  secondColumn,
  features,
  arrayOfLinks,
  primaryButton,
  hasCalculator,
}: FeaturesProps) {
  return (
    <Section className="py-24 bg-background">
      <Container maxWidth={1280}>
        <Flex direction="col" justify="between" className="lg:flex-row">
          <div
            className={`relative w-full  ${
              hasCalculator && "lg:w-[70%] lg:pr-8 lg:border-r border-gray-300"
            }`}
          >
            <Flex
              direction="col"
              gap={8}
              justify="between"
              className="lg:flex-row"
            >
              {/* First column */}
              <div className="relative w-full lg:w-1/2">
                <CaptionAndTitleSection
                  title={title}
                  firstColumn={firstColumn}
                  secondColumn={secondColumn}
                  caption={caption}
                  description={description}
                  primaryButton={primaryButton}
                  arrayOfLinks={arrayOfLinks}
                />
              </div>

              <Flex
                direction="col"
                gap={6}
                className="sm:flex-row lg:flex-col w-full lg:w-1/2"
              >
                <FeatureItems features={features} />
                {firstColumn && (
                  <div className="prose prose-sm md:prose-base lg:prose-lg max-w-none">
                    <PortableText
                      value={firstColumn}
                      components={textComponentBlockStyling}
                      onMissingComponent={false}
                    />
                  </div>
                )}
              </Flex>
            </Flex>
          </div>
          {hasCalculator && (
            <div className="w-full lg:w-[30%] mt-8 lg:mt-4  lg:pl-8">
              <ValveCalculator />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
  firstColumn,
  secondColumn,
  caption,
  description,
  primaryButton,
  arrayOfLinks,
}: {
  title?: string;
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  caption?: string;
  description?: string;
  primaryButton?: LabeledRoute;
  arrayOfLinks?: LabeledRoute[];
}) {
  return (
    <div className="w-full">
      <div className="border-b border-gray-200 cursor-default">
        <div className="inline-flex items-center gap-2 group">
          {primaryButton?.label ? (
            <Link
              href={
                primaryButton?.type === "linkInternal"
                  ? primaryButton?.internalLink
                  : primaryButton?.externalLink
              }
              target={
                primaryButton?.type === "linkInternal" ? "self" : "_blank"
              }
              className="mb-2 text-primary uppercase w-full text-sm md:text-lg hover:underline peer"
            >
              {primaryButton?.label}
            </Link>
          ) : (
            <Text className="mb-2 text-primary uppercase w-full text-sm md:text-lg peer">
              {caption}
            </Text>
          )}
          {primaryButton?.label && (
            <FaArrowRightLong className="mb-2 text-primary animate-pulse scale-0 origin-left transition duration-300 ease-in-out peer-hover:scale-100 peer-hover:opacity-100 opacity-0" />
          )}
        </div>
        {title && (
          <Heading
            type="h2"
            className="!text-black text-2xl md:text-2xl lg:text-[38px] uppercase font-extrabold mb-4"
          >
            {title}
          </Heading>
        )}

        {secondColumn && (
          <div className="prose prose-sm md:prose-base lg:prose-lg max-w-none">
            <PortableText
              value={secondColumn}
              components={textComponentBlock}
              onMissingComponent={false}
            />
          </div>
        )}
        <div className="w-10 h-2 bg-primary" />
      </div>

      {/* <div className="flex flex-col sm:grid sm:grid-cols-2 gap-3 my-6 md:my-8 lg:my-10"> */}
      <div className="w-full flex flex-col xs:flex-row lg:flex-col xl:flex-row flex-wrap  gap-4 py-8">
        {arrayOfLinks?.map((link, index) => (
          <Button
            key={index}
            as="link"
            link={link}
            ariaLabel={`${link?.label}`}
            variant="unstyled"
            className="w-full xs:w-[48%] lg:w-full xl:w-[47%] group"
          >
            <Card variant="normalCard" className="lg:py-6 xl:py-12">
              <div className="flex flex-col items-center gap-3">
                <Image
                  width={50}
                  height={50}
                  alt="file"
                  src="/assets/elements/downloads/file.png"
                  className="group-hover:invert brightness-0 group-hover:opacity-100 opacity-20"
                />
                <div>
                  <div>
                    <Text
                      weight="bold"
                      className="mb-1 lg:text-lg text-base group-hover:text-white uppercase font-bold text-gray-800"
                    >
                      {link?.label}
                    </Text>
                  </div>

                  <div className="group-hover:scale-100 scale-0 origin-left transition duration-300 ease-linear w-10 h-1 bg-white" />
                </div>
              </div>
            </Card>
          </Button>
        ))}
      </div>
    </div>
  );
}

function Buttons({
  primaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex align="center" className="flex items-center justify-start flex-row">
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="maxtonPrimary"
          size="lg"
        >
          {primaryButton.label}
        </Button>
      ) : null}
    </Flex>
  );
}

function FeatureItems({ features }: { features?: ArrayOfImageTitleAndText[] }) {
  if (!features) return null;

  return (
    <div className="w-full">
      <Heading
        type="h3"
        className="text-lg sm:text-xl lg:text-2xl mb-3"
      ></Heading>
      <div className="grid grid-cols-1 gap-4">
        {features.map((feature, index) => (
          <FeatureItem feature={feature} key={feature._key} />
        ))}
      </div>
    </div>
  );
}

function FeatureItem({ feature }: { feature: ArrayOfImageTitleAndText }) {
  return (
    <div className="relative p-4 md:p-5 border border-black/40 rounded-lg shadow-lg">
      <Flex direction="col">
        <div className="items-center gap-2 transition-colors group-hover:border-b border-primary">
          <Text className="text-black font-semibold text-base md:text-lg transition-colors group-hover:text-primary mb-2">
            {feature.title}
          </Text>

          {feature?.firstColumn && (
            <div className="prose prose-sm md:prose-base">
              <PortableText
                value={feature?.firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false}
              />
            </div>
          )}
        </div>
      </Flex>
    </div>
  );
}

export { Features_V };
